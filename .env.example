# Generate a random secret: https://generate-secret.vercel.app/32 or `openssl rand -base64 32`
AUTH_SECRET=****

# The following keys below are automatically created and
# added to your environment when you deploy on vercel

# Instructions to create a Vercel Blob Store here: https://vercel.com/docs/storage/vercel-blob
BLOB_READ_WRITE_TOKEN=****

# Instructions to create a database here: https://vercel.com/docs/storage/vercel-postgres/quickstart
POSTGRES_URL=****

# Instructions to create a Firecrawl API Key here: https://www.mendable.com/docs/api-reference/firecrawl
FIRECRAWL_API_KEY=****

# Exa AI API Key
EXA_API_KEY=****

# E2B API Key
E2B_API_KEY=****

# E2B Sandbox Template ID
SANDBOX_TEMPLATE_ID=****

# Langfuse API Key
LANGFUSE_SECRET_KEY="sk-lf-..."
LANGFUSE_PUBLIC_KEY="pk-lf-..."
LANGFUSE_BASEURL="https://cloud.langfuse.com" # 🇪🇺 EU region, use "https://us.cloud.langfuse.com" for US region

# OpenAI API Key
OPENAI_API_KEY="sk-..."

# Fireworks API Key (Only to replace o3-mini with deepseek-r1)
FIREWORKS_KEY="sk-..."

# Google auth
AUTH_GOOGLE_ID=****
AUTH_GOOGLE_SECRET=****

# Redis URL
REDIS_URL=""

# Secret to call chron jobs with auth
CRON_SECRET=""

AI_GATEWAY_API_KEY=""

AUTH_GITHUB_ID=your_github_client_id
AUTH_GITHUB_SECRET=your_github_client_secret
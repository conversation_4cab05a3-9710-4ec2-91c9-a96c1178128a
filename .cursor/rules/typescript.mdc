---
description: 
globs: 
alwaysApply: false
---

## Typing Guidelines
- Avoid `any` at all cost. The types should work or they indicate a problem.


## Exports / Imports

- Never create index barrel files (index.ts, index.js)    
- Always use direct imports with named exports
- Always use inline interfaces with function parameters


## Examples

### Good - Inline interface with function:
```typescript
export function processData({
  id,
  name,
  options
}: {
  id: string;
  name: string;
  options: ProcessingOptions;
}): ProcessedResult {
  // implementation
}
```

### Bad - Separated interface:
```typescript
interface ProcessDataParams {
  id: string;
  name: string;
  options: ProcessingOptions;
}


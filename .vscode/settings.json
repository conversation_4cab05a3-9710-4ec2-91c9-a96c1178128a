{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "biomejs.biome",
  "[javascript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "typescript.tsdk": "node_modules/typescript/lib",
  // Prefer type only auto imports
  "typescript.preferences.preferTypeOnlyAutoImports": true,
  "editor.wordWrap": "on",
  "editor.codeActionsOnSave": {
    "source.fixAll.biome": "explicit"
  },
  "eslint.workingDirectories": [
    { "pattern": "app/*" },
    { "pattern": "packages/*" },
    { "pattern": "trpc/*" }
  ],
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "coderabbit.autoReviewMode": "disabled",
  "cSpell.words": [
    "firecrawl",
    "tavily"
  ]
}

{"name": "sparka-ai", "version": "0.1.0", "private": true, "license": "MIT", "scripts": {"dev": "next dev --turbo", "build": "tsx lib/db/migrate && next build", "start": "next start", "lint": "next lint && biome lint --write --unsafe", "lint:fix": "next lint --fix && biome lint --write --unsafe", "format": "biome format --write", "db:generate": "drizzle-kit generate", "db:migrate": "npx tsx lib/db/migrate.ts", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push", "db:pull": "drizzle-kit pull", "db:check": "drizzle-kit check", "db:up": "drizzle-kit up", "fetch-models": "node scripts/fetch-models.js", "test": "export PLAYWRIGHT=True && bunx playwright test --workers=4", "test:unit": "vitest run", "test:types": "tsc --noEmit"}, "dependencies": {"@ai-sdk/anthropic": "^2.0.1", "@ai-sdk/gateway": "^1.0.1", "@ai-sdk/google": "^2.0.1", "@ai-sdk/openai": "^2.0.7", "@ai-sdk/provider": "^2.0.0", "@ai-sdk/provider-utils": "^3.0.1", "@ai-sdk/react": "^2.0.1", "@ai-sdk/xai": "^2.0.1", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-python": "^6.1.6", "@codemirror/state": "^6.5.0", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.35.3", "@e2b/code-interpreter": "^1.0.4", "@hookform/resolvers": "^5.0.1", "@lexical/code": "^0.32.1", "@lexical/html": "^0.32.1", "@lexical/link": "^0.32.1", "@lexical/list": "^0.32.1", "@lexical/markdown": "^0.32.1", "@lexical/react": "^0.32.1", "@lexical/rich-text": "^0.32.1", "@lobehub/icons": "^2.23.1", "@mendable/firecrawl-js": "1.29.1", "@opentelemetry/api-logs": "^0.200.0", "@opentelemetry/instrumentation": "^0.200.0", "@opentelemetry/sdk-logs": "^0.200.0", "@phosphor-icons/react": "^2.1.7", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-use-controllable-state": "^1.2.2", "@radix-ui/react-visually-hidden": "^1.2.3", "@redux-devtools/extension": "^3.3.0", "@stepperize/react": "^5.1.5", "@tanstack/react-query": "^5.75.1", "@tanstack/react-query-devtools": "^5.75.1", "@tavily/core": "^0.3.3", "@trpc/client": "^11.1.2", "@trpc/server": "^11.1.2", "@trpc/tanstack-react-query": "^11.1.2", "@types/lodash-es": "^4.17.12", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "@upstash/redis": "^1.35.0", "@vercel/analytics": "^1.3.1", "@vercel/blob": "^0.24.1", "@vercel/kv": "^3.0.0", "@vercel/otel": "^1.10.4", "@vercel/postgres": "^0.10.0", "ai": "5.0.0", "bcrypt-ts": "^5.0.2", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "codemirror": "^6.0.1", "date-fns": "^4.1.0", "diff": "^8.0.2", "dotenv": "^16.5.0", "drizzle-orm": "^0.34.0", "echarts-for-react": "^3.0.2", "exa-js": "^1.5.12", "fast-deep-equal": "^3.1.3", "geist": "^1.3.1", "harden-react-markdown": "^1.0.2", "install": "^0.13.0", "js-tiktoken": "^1.0.19", "langfuse": "^3.37.0", "langfuse-vercel": "^3.37.0", "lexical": "^0.32.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lucide-react": "^0.539.0", "marked": "^15.0.12", "marked-react": "^3.0.0", "motion": "^12.7.4", "nanoid": "^5.0.8", "next": "^15.4.2-canary.12", "next-auth": "5.0.0-beta.25", "next-themes": "^0.3.0", "openai": "^5.8.2", "orderedmap": "^2.1.1", "papaparse": "^5.5.2", "postgres": "^3.4.4", "react": "19.0.0-rc-45804af1-20241021", "react-data-grid": "7.0.0-beta.47", "react-dom": "19.0.0-rc-45804af1-20241021", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "react-scan": "^0.4.3", "react-syntax-highlighter": "^15.6.1", "react-tweet": "^3.2.2", "redis": "^5.5.6", "rehype-katex": "^7.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "resumable-stream": "2.2.0", "server-only": "^0.0.1", "sonner": "^2.0.5", "superjson": "^2.2.2", "swr": "^2.2.5", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "throttleit": "^2.1.0", "use-stick-to-bottom": "^1.1.1", "usehooks-ts": "^3.1.0", "uuid": "^11.1.0", "vaul": "^1.1.2", "vercel": "^42.1.1", "zod": "^3.25.0", "zustand": "^5.0.6"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@playwright/test": "^1.50.1", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/typography": "^0.5.15", "@types/d3-scale": "^4.0.8", "@types/diff": "^8.0.0", "@types/node": "^22.8.6", "@types/papaparse": "^5.3.15", "@types/pdf-parse": "^1.1.4", "@types/react": "^18", "@types/react-dom": "^18", "drizzle-kit": "^0.25.0", "eslint": "^8.57.0", "eslint-config-next": "15.4.2", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-tailwindcss": "^3.17.5", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.1", "typescript": "5.8.3", "vitest": "^3.2.4"}, "packageManager": "bun@1.1.34"}